'use client';

import { memo, useState, useRef, useEffect } from 'react';
import { createStyles } from 'antd-style';
import { Flexbox } from 'react-layout-kit';
import { Send, Paperclip, Mic, Square, Bot, AlertTriangle, DollarSign, Gift } from 'lucide-react';
import { ActionIcon } from '@lobehub/ui';
import { Input, Space, Typography, Alert, message as antdMessage, Button, Modal } from 'antd';
import { SimpleModelSelector } from '@/components/ModelSelector';
import RemainingCountDisplay, { RemainingCountDisplayRef } from '@/components/chat/RemainingCountDisplay';
import AgentPricingModal from '@/components/chat/AgentPricingModal';
import RedeemCodeForm from '@/components/redemption/RedeemCodeForm';
import { useChatStore, DEFAULT_AGENT_ID } from '@/store/chat';
import { useAIProviderConfigsStore } from '@/store/aiProviderConfigs';
import { useAuthStore } from '@/store/auth';
import { useAgentStore } from '@/store/agent';
import { AIProvider } from '@/types/ai-provider';
const CHAT_TEXTAREA_HEIGHT = 160;

const { TextArea } = Input;
const { Text } = Typography;

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    flex-shrink: 0;
    background: ${token.colorBgContainer};
    border-top: 1px solid ${token.colorBorderSecondary};
    padding: 16px;

    @media (max-width: 768px) {
      padding: 12px;
      /* 确保在移动端键盘弹出时不被遮挡 */
      position: relative;
      z-index: 10;
      flex-shrink: 0;
    }
  `,

  inputWrapper: css`
    max-width: 800px;
    margin: 0 auto;
    width: 100%;

    @media (max-width: 768px) {
      max-width: 100%;
    }
  `,

  toolbar: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 0 4px;

    @media (max-width: 768px) {
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 8px;
      padding: 0;
    }
  `,

  modelSelector: css`
    min-width: 200px;

    @media (max-width: 768px) {
      min-width: 150px;
      flex: 1;
    }
  `,
  
  inputContainer: css`
    display: flex;
    align-items: flex-end;
    gap: 8px;
    background: ${token.colorBgElevated};
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 12px;
    padding: 12px;
    transition: all 0.2s ease;

    &:focus-within {
      border-color: ${token.colorPrimary};
      box-shadow: 0 0 0 2px ${token.colorPrimary}20;
    }

    @media (max-width: 768px) {
      padding: 10px;
      gap: 6px;
      border-radius: 8px;
    }
  `,

  textArea: css`
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    outline: none;
    font-size: 14px;
    line-height: 1.5;
    color: ${token.colorText};
    min-height: 20px;
    max-height: 200px;

    &::placeholder {
      color: ${token.colorTextTertiary};
    }

    &:focus {
      box-shadow: none;
    }

    @media (max-width: 768px) {
      font-size: 16px; /* 防止iOS缩放 */
      max-height: 120px;
    }
  `,

  actionButtons: css`
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;

    @media (max-width: 768px) {
      gap: 2px;
    }
  `,
  
  sendButton: css`
    background: ${token.colorPrimary};
    color: white;
    
    &:hover {
      background: ${token.colorPrimaryHover};
    }
    
    &:disabled {
      background: ${token.colorBgTextHover};
      color: ${token.colorTextDisabled};
    }
  `,
  
  stopButton: css`
    background: ${token.colorError};
    color: white;
    
    &:hover {
      background: ${token.colorErrorHover};
    }
  `,
  
  footer: css`
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 8px;
    font-size: 12px;
    color: ${token.colorTextTertiary};
  `,
  
  shortcut: css`
    display: flex;
    align-items: center;
    gap: 4px;
    
    kbd {
      background: ${token.colorFillQuaternary};
      border: 1px solid ${token.colorBorderSecondary};
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 11px;
      font-family: monospace;
    }
  `,
}));

interface ChatInputNewProps {
  onSend?: (message: string) => void;
  loading?: boolean;
  placeholder?: string;
}

const ChatInputNew = memo<ChatInputNewProps>(({
  onSend,
  loading = false,
  placeholder = "输入消息开始对话...",
}) => {
  const { styles } = useStyles();
  const [message, setMessage] = useState('');
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const remainingCountRef = useRef<RemainingCountDisplayRef>(null);

  // 弹窗状态
  const [pricingModalVisible, setPricingModalVisible] = useState(false);
  const [redeemModalVisible, setRedeemModalVisible] = useState(false);

  const { activeSessionId, currentSessions, updateSessionModel, currentAgentStatus, checkAgentStatus } = useChatStore();
  const { user } = useAuthStore();
  const { userSettings, loadUserSettings } = useAIProviderConfigsStore();
  const { agents } = useAgentStore();

  // 获取当前活跃会话
  const activeSession = currentSessions.find(s => s.id === activeSessionId);

  // 判断当前会话是否为coze智能体
  const isCozeAgent = Boolean(
    activeSession?.agentId &&
    activeSession.agentId !== DEFAULT_AGENT_ID &&
    agents.find(agent => agent.id === activeSession.agentId && agent.type === 'coze')
  );

  // 获取当前智能体信息
  const currentAgent = activeSession?.agentId && activeSession.agentId !== DEFAULT_AGENT_ID
    ? agents.find(agent => agent.id === activeSession.agentId)
    : null;

  // 加载用户设置
  useEffect(() => {
    if (user?.id) {
      loadUserSettings(user.id);
    }
  }, [user?.id, loadUserSettings]);

  // 检查当前会话的智能体状态
  useEffect(() => {
    if (activeSession?.agentId && activeSession.agentId !== DEFAULT_AGENT_ID) {
      // 如果当前智能体状态不匹配，重新检查
      if (!currentAgentStatus || currentAgentStatus.agentId !== activeSession.agentId) {
        checkAgentStatus(activeSession.agentId);
      }
    } else {
      // 清除智能体状态（普通会话）
      if (currentAgentStatus) {
        useChatStore.setState({ currentAgentStatus: null });
      }
    }
  }, [activeSession?.agentId, currentAgentStatus, checkAgentStatus]);

  const handleSend = () => {
    if (!message.trim() || loading) return;

    // 检查智能体状态
    if (activeSession?.agentId && activeSession.agentId !== DEFAULT_AGENT_ID) {
      if (currentAgentStatus) {
        if (!currentAgentStatus.exists) {
          antdMessage.error('该智能体已不存在，无法发送消息');
          return;
        }
        if (!currentAgentStatus.enabled) {
          antdMessage.error('该智能体已被禁用，无法发送消息');
          return;
        }
      }
    }

    onSend?.(message.trim());
    setMessage('');

    // 重置文本框高度
    if (textAreaRef.current) {
      textAreaRef.current.style.height = 'auto';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift + Enter 换行
        return;
      } else {
        // Enter 发送
        e.preventDefault();
        handleSend();
      }
    }
  };

  const handleStop = () => {
    // TODO: 实现停止生成
  };

  const handleFileUpload = () => {
    // TODO: 实现文件上传
    console.log('文件上传');
  };

  const handleVoiceInput = () => {
    // TODO: 实现语音输入
    console.log('语音输入');
  };

  const handleModelChange = (provider: AIProvider, model: string) => {
    if (activeSessionId) {
      updateSessionModel(activeSessionId, provider, model);
    }
  };

  // 计算是否应该禁用输入
  const isAgentUnavailable = Boolean(activeSession?.agentId &&
    activeSession.agentId !== DEFAULT_AGENT_ID &&
    currentAgentStatus &&
    (!currentAgentStatus.exists || !currentAgentStatus.enabled));

  // 获取禁用原因
  const getDisabledReason = () => {
    if (!isAgentUnavailable || !currentAgentStatus) return null;

    if (!currentAgentStatus.exists) {
      return `智能体"${currentAgentStatus.name || '未知'}"已不存在`;
    }
    if (!currentAgentStatus.enabled) {
      return `智能体"${currentAgentStatus.name || '未知'}"已被禁用`;
    }
    return null;
  };

  // 处理定价弹窗
  const handleShowPricing = () => {
    if (activeSession?.agentId && activeSession.agentId !== DEFAULT_AGENT_ID) {
      setPricingModalVisible(true);
    } else {
      antdMessage.info('当前会话不是智能体会话，无需查看定价信息');
    }
  };

  // 处理兑换弹窗
  const handleShowRedeem = () => {
    setRedeemModalVisible(true);
  };

  const { TextArea } = Input;

  // 自动调整文本框高度
  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = 'auto';
      textAreaRef.current.style.height = `${textAreaRef.current.scrollHeight}px`;
    }
  }, [message]);

  return (
    <div className={`${styles.container} chat-input-container`}>
      <div className={styles.inputWrapper}>
        {/* 工具栏 */}
        <div className={styles.toolbar}>
          <Space align="center" size="middle">
            <Bot size={16} />
            <Text type="secondary" style={{ fontSize: 12 }}>
              当前模型:
            </Text>
            {isCozeAgent ? (
              <Text style={{ fontSize: 12, color: '#666' }}>
                智能体内置模型
              </Text>
            ) : (
              <SimpleModelSelector
                className={styles.modelSelector}
                value={userSettings ? {
                  provider: userSettings.currentProvider,
                  model: userSettings.currentModel
                } : undefined}
                onChange={handleModelChange}
                size="small"
              />
            )}
          </Space>
          <Space align="center" size="middle">
            {/* 智能体会话的操作按钮 */}
            {activeSession?.agentId && activeSession.agentId !== DEFAULT_AGENT_ID && (
              <>
                <Button
                  type="text"
                  size="small"
                  icon={<DollarSign size={14} />}
                  onClick={handleShowPricing}
                  style={{ fontSize: '12px', height: '24px', padding: '0 8px' }}
                >
                  定价
                </Button>
                <Button
                  type="text"
                  size="small"
                  icon={<Gift size={14} />}
                  onClick={handleShowRedeem}
                  style={{ fontSize: '12px', height: '24px', padding: '0 8px' }}
                >
                  兑换
                </Button>
                <RemainingCountDisplay
                  ref={remainingCountRef}
                  agentId={activeSession.agentId}
                />
              </>
            )}
          </Space>
        </div>

        {/* 智能体不可用警告 */}
        {isAgentUnavailable && (
          <Alert
            message={`${getDisabledReason()}，但您仍可以查看历史对话和管理会话`}
            type="warning"
            icon={<AlertTriangle size={16} />}
            style={{ marginBottom: 12 }}
            showIcon
          />
        )}

        <div className={styles.inputContainer}>
          <textarea
            ref={textAreaRef}
            className={styles.textArea}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={isAgentUnavailable ? getDisabledReason() || placeholder : placeholder}
            disabled={loading || isAgentUnavailable}
            rows={1}
          />
          
          <div className={styles.actionButtons}>
            <ActionIcon
              icon={Paperclip}
              size={{ blockSize: 32, size: 16 }}
              onClick={handleFileUpload}
              disabled={isAgentUnavailable}
              title="上传文件"
            />
            <ActionIcon
              icon={Mic}
              size={{ blockSize: 32, size: 16 }}
              onClick={handleVoiceInput}
              disabled={isAgentUnavailable}
              title="语音输入"
            />

            {loading ? (
              <ActionIcon
                className={styles.stopButton}
                icon={Square}
                size={{ blockSize: 32, size: 16 }}
                onClick={handleStop}
                title="停止生成"
              />
            ) : (
              <ActionIcon
                className={styles.sendButton}
                icon={Send}
                size={{ blockSize: 32, size: 16 }}
                onClick={handleSend}
                disabled={!message.trim() || isAgentUnavailable}
                title={isAgentUnavailable ? getDisabledReason() || "发送消息" : "发送消息"}
              />
            )}
          </div>
        </div>
        
        <div className={styles.footer}>
          <div className={styles.shortcut}>
            <kbd>Enter</kbd>
            <span>发送</span>
          </div>
          <div className={styles.shortcut}>
            <kbd>Shift</kbd>
            <span>+</span>
            <kbd>Enter</kbd>
            <span>换行</span>
          </div>
        </div>
      </div>

      {/* 定价信息弹窗 */}
      {activeSession?.agentId && activeSession.agentId !== DEFAULT_AGENT_ID && (
        <AgentPricingModal
          visible={pricingModalVisible}
          onClose={() => setPricingModalVisible(false)}
          agentId={activeSession.agentId}
          agentName={activeSession.agentName || '智能体'}
        />
      )}

      {/* 兑换码弹窗 */}
      <Modal
        title="兑换码"
        open={redeemModalVisible}
        onCancel={() => setRedeemModalVisible(false)}
        footer={null}
        width={500}
        destroyOnClose
      >
        <RedeemCodeForm
          agentId={activeSession?.agentId && activeSession.agentId !== DEFAULT_AGENT_ID ? activeSession.agentId : undefined}
        />
      </Modal>
    </div>
  );
});

export default ChatInputNew;
