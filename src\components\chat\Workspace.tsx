'use client';

import { useTheme } from 'antd-style';
import { PropsWithChildren, memo } from 'react';
import { Flexbox } from 'react-layout-kit';
import ChatHeader from './ChatHeader';

const Workspace = memo<PropsWithChildren>(({ children }) => {
  const theme = useTheme();
  return (
    <Flexbox
      flex={1}
      height={'100%'}
      style={{
        background: theme.colorBgContainer,
        overflow: 'hidden',
        position: 'relative',
      }}
      className="workspace-container"
    >
      <ChatHeader />
      <Flexbox
        flex={1}
        style={{
          overflow: 'hidden',
          position: 'relative',
        }}
        data-chat-container
        className="chat-container"
      >
        {children}
      </Flexbox>
    </Flexbox>
  );
});

export default Workspace;
