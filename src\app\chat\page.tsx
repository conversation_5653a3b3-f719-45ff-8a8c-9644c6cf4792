'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createStyles } from 'antd-style';
import { message } from 'antd';
import { Flexbox } from 'react-layout-kit';
import { useChatStore } from '@/store/chat';
import { useAuthStore } from '@/store/auth';
import MainSideBar from '@/components/layout/MainSideBar';
import CurrentSessionPanel from '@/components/chat/CurrentSessionPanel';
import HistoryPanel from '@/components/chat/HistoryPanel';
import Workspace from '@/components/chat/Workspace';
import ChatList from '@/components/chat/ChatList';
import ChatInputNew from '@/components/chat/ChatInputNew';
import LoadingSpinner from '@/components/common/LoadingSpinner';

const useStyles = createStyles(({ token, css }) => ({
  layout: css`
    height: 100vh;
    display: flex;
    background: ${token.colorBgLayout};
    max-width: 100%;
    overflow: hidden;
    position: relative;

    @media (max-width: 768px) {
      flex-direction: column;
      height: auto;
      min-height: 100vh;
      overflow: visible;
    }
  `,

  mobileHeader: css`
    display: none;

    @media (max-width: 768px) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: ${token.colorBgContainer};
      border-bottom: 1px solid ${token.colorBorderSecondary};
      min-height: 56px;

      .title {
        font-size: 16px;
        font-weight: 600;
        color: ${token.colorText};
      }
    }
  `,

  mainContent: css`
    flex: 1;
    display: flex;
    overflow: hidden;

    @media (max-width: 768px) {
      flex-direction: column;
      flex: none;
      overflow: visible;
      min-height: calc(100vh - 56px); /* 减去头部高度 */
    }
  `,
}));





const ChatPage: React.FC = () => {
  const { styles } = useStyles();
  const router = useRouter();
  const { checkLoginStatus } = useAuthStore();
  const { sendMessage, isLoading } = useChatStore();
  const [mounted, setMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showSessionPanel, setShowSessionPanel] = useState(false);

  // 检查登录状态
  useEffect(() => {
    const initAuth = async () => {
      await checkLoginStatus();

      const currentState = useAuthStore.getState();
      if (!currentState.isLoggedIn) {
        router.replace('/login');
        return;
      }
      setMounted(true);
    };

    initAuth();
  }, [router, checkLoginStatus]);

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (!mobile) {
        setShowSessionPanel(false);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const handleSendMessage = async (content: string) => {
    try {
      await sendMessage(content);
    } catch (error) {
      message.error('发送消息失败，请重试');
    }
  };




  // 如果还未挂载完成，显示加载状态
  if (!mounted) {
    return <LoadingSpinner />;
  }

  return (
    <div className={styles.layout}>
      {/* 桌面端布局 */}
      {!isMobile && (
        <>
          {/* 最左边的主导航侧边栏 */}
          <MainSideBar />

          {/* 当前会话面板 */}
          <CurrentSessionPanel />

          {/* 主工作区 */}
          <Flexbox
            flex={1}
            horizontal
            style={{ overflow: 'hidden', position: 'relative' }}
          >
            {/* 聊天区域 */}
            <Flexbox
              flex={1}
              style={{ overflow: 'hidden', position: 'relative' }}
            >
              <Workspace>
                <ChatList />
                <ChatInputNew
                  onSend={handleSendMessage}
                  loading={isLoading}
                />
              </Workspace>
            </Flexbox>

            {/* 右侧历史会话面板 */}
            <HistoryPanel />
          </Flexbox>
        </>
      )}

      {/* 移动端布局 */}
      {isMobile && (
        <>
          {/* 移动端头部 */}
          <div className={styles.mobileHeader}>
            <div className="title">微甜 AI Studio</div>
            <div style={{ display: 'flex', gap: '8px' }}>
              <button
                onClick={() => setShowSessionPanel(!showSessionPanel)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '18px',
                  cursor: 'pointer',
                  padding: '8px',
                }}
              >
                📋
              </button>
            </div>
          </div>

          {/* 移动端主内容 */}
          <div className={styles.mainContent}>
            {/* 聊天区域 */}
            <Flexbox
              flex={1}
              style={{ overflow: 'hidden', position: 'relative' }}
            >
              <Workspace>
                <ChatList mobile />
                <ChatInputNew
                  onSend={handleSendMessage}
                  loading={isLoading}
                />
              </Workspace>
            </Flexbox>
          </div>

          {/* 移动端会话面板覆盖层 */}
          {showSessionPanel && (
            <>
              <div
                style={{
                  position: 'fixed',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'rgba(0, 0, 0, 0.5)',
                  zIndex: 999,
                }}
                onClick={() => setShowSessionPanel(false)}
              />
              <div
                style={{
                  position: 'fixed',
                  top: 0,
                  left: 0,
                  width: '85vw',
                  height: '100vh',
                  zIndex: 1000,
                }}
              >
                <CurrentSessionPanel />
              </div>
            </>
          )}
        </>
      )}
    </div>
  );
};

export default ChatPage;