'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { createStyles } from 'antd-style';
import { Form, Input, Button, Checkbox, Divider, App, Modal } from 'antd';
import { Mail, Lock, Eye, EyeOff, User } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/store/auth';

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    min-height: 100vh;
    display: flex;
    background: ${token.colorBgLayout};
  `,
  
  leftPanel: css`
    flex: 1;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60px;
    color: white;
    position: relative;
    overflow: hidden;
  `,
  
  brandContent: css`
    text-align: center;
    z-index: 1;
    
    .logo {
      width: 80px;
      height: 80px;
      margin: 0 auto 24px;
      background: url('/favicon.png') no-repeat center;
      background-size: contain;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .title {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 16px;
      background: linear-gradient(135deg, #ffffff, #e2e8f0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .subtitle {
      font-size: 20px;
      color: #cbd5e1;
      margin-bottom: 32px;
      line-height: 1.6;
    }
    
    .description {
      font-size: 16px;
      color: #94a3b8;
      line-height: 1.8;
      max-width: 400px;
    }
  `,
  
  rightPanel: css`
    flex: 1;
    background: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60px;
  `,
  
  formContainer: css`
    width: 100%;
    max-width: 400px;
    
    .form-header {
      text-align: center;
      margin-bottom: 40px;
      
      .welcome {
        font-size: 32px;
        font-weight: 700;
        color: ${token.colorText};
        margin-bottom: 8px;
      }
      
      .subtitle {
        font-size: 16px;
        color: ${token.colorTextSecondary};
      }
    }
  `,
  
  formItem: css`
    margin-bottom: 24px;
    
    .ant-form-item-label > label {
      font-weight: 500;
      color: ${token.colorText};
    }
    
    .ant-input {
      height: 48px;
      border-radius: 8px;
      border: 1px solid ${token.colorBorder};
      padding: 0 16px;
      font-size: 16px;
      
      &:focus {
        border-color: ${token.colorPrimary};
        box-shadow: 0 0 0 2px ${token.colorPrimary}20;
      }
    }
    
    .ant-input-password {
      .ant-input {
        padding-right: 50px;
      }
    }
  `,
  
  submitButton: css`
    width: 100%;
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(135deg, ${token.colorPrimary}, #8b5cf6);
    border: none;
    margin-bottom: 24px;
    
    &:hover {
      background: linear-gradient(135deg, ${token.colorPrimaryHover}, #7c3aed);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px ${token.colorPrimary}40;
    }
  `,
  
  dividerContainer: css`
    margin: 24px 0;
    
    .ant-divider-inner-text {
      color: ${token.colorTextSecondary};
      font-size: 14px;
    }
  `,
  
  socialButton: css`
    width: 100%;
    height: 48px;
    border-radius: 8px;
    border: 1px solid ${token.colorBorder};
    background: white;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 16px;
    font-weight: 500;
    color: ${token.colorText};
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      border-color: ${token.colorPrimary};
      background: ${token.colorPrimaryBg};
      transform: translateY(-1px);
    }
    
    .icon {
      width: 20px;
      height: 20px;
    }
  `,
  
  switchMode: css`
    text-align: center;
    margin-top: 24px;
    color: ${token.colorTextSecondary};
    
    .link {
      color: ${token.colorPrimary};
      cursor: pointer;
      font-weight: 500;
      
      &:hover {
        color: ${token.colorPrimaryHover};
      }
    }
  `,
}));

function AuthContent() {
  const { styles } = useStyles();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [forgotPasswordVisible, setForgotPasswordVisible] = useState(false);
  const [forgotPasswordForm] = Form.useForm();
  const [sendingResetEmail, setSendingResetEmail] = useState(false);


  // 检查URL参数
  useEffect(() => {
    const mode = searchParams.get('mode');
    if (mode === 'register') {
      setIsLogin(false);
    }
    
    const verified = searchParams.get('verified');
    if (verified === 'true') {
      message.success('邮箱验证成功，现在可以登录了！');
    }
    
    const error = searchParams.get('error');
    if (error === 'verification_failed') {
      message.error('邮箱验证失败，请重试');
    }
  }, [searchParams, message]);

  // 验证码倒计时
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);

  const { loginWithAPI } = useAuthStore();

  // 发送验证码
  const handleSendVerificationCode = async () => {
    const email = form.getFieldValue('email');
    if (!email) {
      message.error('请先输入邮箱地址');
      return;
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      message.error('邮箱格式不正确');
      return;
    }

    setSendingCode(true);
    try {
      const response = await fetch('/api/auth/send-verification-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();
      if (result.success) {
        message.success(result.message);
        setCountdown(60); // 60秒倒计时
      } else {
        message.error(result.error);
      }
    } catch (error) {
      message.error('发送验证码失败，请重试');
    } finally {
      setSendingCode(false);
    }
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      if (isLogin) {
        // 使用新的API登录方法
        const success = await loginWithAPI(values.email, values.password, values.rememberMe);
        if (success) {
          message.success('登录成功！');
          router.push('/chat');
        } else {
          // 获取最新的错误信息
          const currentError = useAuthStore.getState().error;
          if (currentError) {
            message.error(currentError);
          } else {
            message.error('登录失败，请重试');
          }
        }
      } else {
        // 注册逻辑
        const response = await fetch('/api/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(values),
        });

        const result = await response.json();

        if (result.success) {
          message.success('注册成功！邮箱已验证，可以直接登录');
          setIsLogin(true);
          form.resetFields();
        } else {
          message.error(result.error || '注册失败');
        }
      }
    } catch (error) {
      message.error('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = (provider: string) => {
    const state = Math.random().toString(36).substring(2, 11);

    if (provider === '微信') {
      window.location.href = `/api/auth/oauth/wechat?state=${state}`;
    } else if (provider === 'QQ') {
      window.location.href = `/api/auth/oauth/qq?state=${state}`;
    } else {
      message.info(`${provider}登录功能开发中...`);
    }
  };

  // 处理忘记密码
  const handleForgotPassword = () => {
    setForgotPasswordVisible(true);
  };

  // 发送重置密码邮件
  const handleSendResetEmail = async (values: { email: string }) => {
    setSendingResetEmail(true);
    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: values.email }),
      });

      const result = await response.json();
      if (result.success) {
        message.success(result.message);
        setForgotPasswordVisible(false);
        forgotPasswordForm.resetFields();
      } else {
        message.error(result.error);
      }
    } catch (error) {
      message.error('发送重置密码邮件失败，请重试');
    } finally {
      setSendingResetEmail(false);
    }
  };

  return (
    <div className={styles.container}>
      {/* 左侧品牌展示 */}
      <div className={styles.leftPanel}>
        <div className={styles.brandContent}>
          <div className="logo"></div>
          <h1 className="title">微甜 AI Studio</h1>
          <p className="subtitle">让创意无限可能</p>
          <p className="description">
          欢迎来到微甜AlStudio，这里有很多好玩实用的影视广告AI工具，助力你的影视学习工作
          </p>
        </div>
      </div>

      {/* 右侧表单 */}
      <div className={styles.rightPanel}>
        <div className={styles.formContainer}>
          <div className="form-header">
            <h2 className="welcome">
              {isLogin ? '欢迎回来' : '创建账户'}
            </h2>
            <p className="subtitle">
              {isLogin ? '请输入您的登录信息' : '请填写注册信息'}
            </p>
          </div>

          <Form
            form={form}
            onFinish={handleSubmit}
            layout="vertical"
            size="large"
          >
            {!isLogin && (
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                ]}
                className={styles.formItem}
              >
                <Input
                  prefix={<User size={20} />}
                  placeholder="输入用户名"
                />
              </Form.Item>
            )}

            <Form.Item
              name="email"
              label={isLogin ? "用户名/邮箱" : "邮箱地址"}
              rules={[
                { required: true, message: isLogin ? '请输入用户名或邮箱' : '请输入邮箱' },
                ...(isLogin ? [] : [{ type: 'email' as const, message: '邮箱格式不正确' }]),
              ]}
              className={styles.formItem}
            >
              <Input
                prefix={isLogin ? <User size={20} /> : <Mail size={20} />}
                placeholder={isLogin ? "输入用户名或邮箱" : "输入邮箱地址"}
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                ...(isLogin ? [] : [
                  { min: 8, message: '密码至少8个字符' },
                  {
                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                    message: '密码必须包含大小写字母和数字',
                  },
                ]),
              ]}
              className={styles.formItem}
            >
              <Input.Password
                prefix={<Lock size={20} />}
                placeholder="输入密码"
                iconRender={(visible) =>
                  visible ? <Eye size={20} /> : <EyeOff size={20} />
                }
              />
            </Form.Item>

            {!isLogin && (
              <Form.Item
                name="confirmPassword"
                label="确认密码"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
                className={styles.formItem}
              >
                <Input.Password
                  prefix={<Lock size={20} />}
                  placeholder="再次输入密码"
                  iconRender={(visible) =>
                    visible ? <Eye size={20} /> : <EyeOff size={20} />
                  }
                />
              </Form.Item>
            )}

            {!isLogin && (
              <Form.Item
                name="verificationCode"
                label="邮箱验证码"
                rules={[
                  { required: true, message: '请输入邮箱验证码' },
                  { len: 6, message: '验证码为6位数字' },
                ]}
                className={styles.formItem}
              >
                <Input
                  prefix={<Mail size={20} />}
                  placeholder="输入6位验证码"
                  maxLength={6}
                  suffix={
                    <Button
                      type="link"
                      size="small"
                      onClick={handleSendVerificationCode}
                      loading={sendingCode}
                      disabled={countdown > 0}
                      style={{ padding: 0, height: 'auto' }}
                    >
                      {countdown > 0 ? `${countdown}s` : '发送验证码'}
                    </Button>
                  }
                />
              </Form.Item>
            )}

            {isLogin && (
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 24 }}>
                <Form.Item name="rememberMe" valuePropName="checked" style={{ margin: 0 }}>
                  <Checkbox>记住30天</Checkbox>
                </Form.Item>
                <a
                  href="#"
                  style={{ color: '#3b82f6' }}
                  onClick={(e) => {
                    e.preventDefault();
                    handleForgotPassword();
                  }}
                >
                  忘记密码？
                </a>
              </div>
            )}

            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className={styles.submitButton}
            >
              {loading ? '处理中...' : (isLogin ? '登录' : '注册')}
            </Button>
          </Form>

          <Divider className={styles.dividerContainer}>或</Divider>

          <div className={styles.socialButton} onClick={() => handleSocialLogin('微信')}>
            <span className="icon">🔥</span>
            <span>使用微信登录</span>
          </div>

          <div className={styles.socialButton} onClick={() => handleSocialLogin('QQ')}>
            <span className="icon">🐧</span>
            <span>使用QQ登录</span>
          </div>

          <div className={styles.switchMode}>
            {isLogin ? '还没有账户？' : '已有账户？'}
            <span
              className="link"
              onClick={() => setIsLogin(!isLogin)}
            >
              {isLogin ? '立即注册' : '立即登录'}
            </span>
          </div>
        </div>
      </div>

      {/* 忘记密码Modal */}
      <Modal
        title="重置密码"
        open={forgotPasswordVisible}
        onCancel={() => {
          setForgotPasswordVisible(false);
          forgotPasswordForm.resetFields();
        }}
        footer={null}
        width={400}
      >
        <p style={{ marginBottom: 20, color: '#666' }}>
          请输入您的邮箱地址，我们将发送重置密码链接到您的邮箱。
        </p>
        <Form
          form={forgotPasswordForm}
          onFinish={handleSendResetEmail}
          layout="vertical"
        >
          <Form.Item
            name="email"
            label="邮箱地址"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '邮箱格式不正确' },
            ]}
          >
            <Input
              prefix={<Mail size={20} />}
              placeholder="请输入您的邮箱地址"
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={sendingResetEmail}
              style={{ width: '100%' }}
            >
              {sendingResetEmail ? '发送中...' : '发送重置链接'}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

export default function AuthPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载...</p>
        </div>
      </div>
    }>
      <AuthContent />
    </Suspense>
  );
}
