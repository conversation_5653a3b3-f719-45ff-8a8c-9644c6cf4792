'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Modal, Form, Input, Upload, Avatar, Button, message, Space } from 'antd';
import { User, Camera, Upload as UploadIcon } from 'lucide-react';
import { createStyles } from 'antd-style';
import { useAuthStore } from '@/store/auth';
import { compressImage, validateImageFile, generateDefaultAvatar } from '@/utils/imageUtils';
import type { UploadFile, UploadProps } from 'antd';

const useStyles = createStyles(({ token, css }) => ({
  modalContent: css`
    .ant-modal-body {
      padding: 24px;
    }

    @media (max-width: 768px) {
      .ant-modal {
        margin: 16px;
        max-width: calc(100vw - 32px);
      }

      .ant-modal-body {
        padding: 16px;
      }
    }
  `,
  
  avatarSection: css`
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: ${token.colorFillAlter};
    border-radius: 12px;
    
    .avatar-container {
      position: relative;
      margin-bottom: 16px;
      
      .avatar-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        cursor: pointer;
        
        &:hover {
          opacity: 1;
        }
        
        .camera-icon {
          color: white;
        }
      }
    }
    
    .upload-hint {
      font-size: 12px;
      color: ${token.colorTextSecondary};
      text-align: center;
      margin-top: 8px;
    }
  `,
  
  formSection: css`
    .ant-form-item-label > label {
      font-weight: 500;
    }
  `,
  
  actionButtons: css`
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid ${token.colorBorderSecondary};
  `,
}));

interface ProfileModalProps {
  visible: boolean;
  onClose: () => void;
}

const ProfileModal: React.FC<ProfileModalProps> = ({ visible, onClose }) => {
  const { styles } = useStyles();
  const { user, updateUserProfile } = useAuthStore();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string>(user?.avatar || '/favicon.png');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理头像上传
  const handleAvatarChange: UploadProps['onChange'] = (info) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }
    
    if (info.file.status === 'done') {
      // 这里应该是从服务器返回的URL
      // 现在我们模拟一个本地预览
      const file = info.file.originFileObj;
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          setAvatarUrl(result);
          setLoading(false);
        };
        reader.readAsDataURL(file);
      }
    }
    
    if (info.file.status === 'error') {
      message.error('头像上传失败');
      setLoading(false);
    }
  };

  // 自定义上传函数
  const customUpload = ({ file, onSuccess, onError }: any) => {
    // 模拟上传过程
    setTimeout(() => {
      try {
        // 这里应该调用真实的上传API
        onSuccess('ok');
      } catch (error) {
        onError(error);
      }
    }, 1000);
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 更新用户信息
      const success = await updateUserProfile({
        username: values.username,
        email: values.email,
        avatar: avatarUrl,
      });

      if (success) {
        message.success('个人信息更新成功');
        onClose();
      } else {
        message.error('更新失败，请重试');
      }
    } catch (error) {
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setAvatarUrl(user?.avatar || '/favicon.png');
  };

  // 重置头像为默认头像
  const handleResetAvatar = () => {
    if (user?.username) {
      const defaultAvatar = generateDefaultAvatar(user.username);
      setAvatarUrl(defaultAvatar);
    } else {
      setAvatarUrl('/favicon.png');
    }
  };

  // 当弹窗打开时，重新初始化数据
  useEffect(() => {
    if (visible && user) {
      setAvatarUrl(user.avatar || '/favicon.png');
      form.setFieldsValue({
        username: user.username,
        email: user.email,
      });
    }
  }, [visible, user, form]);

  // 触发文件选择
  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  // 处理文件选择
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件
    const validation = validateImageFile(file);
    if (!validation.valid) {
      message.error(validation.error);
      return;
    }

    try {
      setLoading(true);
      // 压缩图片
      const compressedImage = await compressImage(file, 200, 200, 0.8);
      setAvatarUrl(compressedImage);
      message.success('头像上传成功');
    } catch (error) {
      message.error('图片处理失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Modal
        title={
          <Space>
            <User size={20} />
            <span>个人信息</span>
          </Space>
        }
        open={visible}
        onCancel={onClose}
        footer={null}
        width={480}
        className={styles.modalContent}
        destroyOnClose
      >
        {/* 头像部分 */}
        <div className={styles.avatarSection}>
          <div className="avatar-container">
            <Avatar
              size={80}
              src={avatarUrl}
              icon={<User />}
            />
            <div className="avatar-overlay" onClick={handleAvatarClick}>
              <Camera size={24} className="camera-icon" />
            </div>
          </div>
          <Space wrap>
            <Button
              type="link"
              icon={<UploadIcon size={16} />}
              onClick={handleAvatarClick}
            >
              上传头像
            </Button>
            <Button
              type="link"
              onClick={handleResetAvatar}
              style={{ color: '#666' }}
            >
              生成头像
            </Button>
          </Space>
          <div className="upload-hint">
            支持 JPG、PNG、GIF、WebP 格式，文件大小不超过 2MB<br/>
            上传的图片会自动压缩为 200x200 像素
          </div>
        </div>

        {/* 表单部分 */}
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            username: user?.username,
            email: user?.email,
          }}
          className={styles.formSection}
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 2, message: '用户名至少2个字符' },
              { max: 20, message: '用户名最多20个字符' },
            ]}
          >
            <Input
              placeholder="请输入用户名"
              prefix={<User size={16} />}
            />
          </Form.Item>

          <Form.Item
            label="邮箱"
            name="email"
            rules={[
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input
              placeholder="请输入邮箱地址（可选）"
              type="email"
            />
          </Form.Item>
        </Form>

        {/* 操作按钮 */}
        <div className={styles.actionButtons}>
          <Button onClick={handleReset}>
            重置
          </Button>
          <Button onClick={onClose}>
            取消
          </Button>
          <Button
            type="primary"
            loading={loading}
            onClick={handleSubmit}
          >
            保存
          </Button>
        </div>
      </Modal>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />
    </>
  );
};

export default ProfileModal;
