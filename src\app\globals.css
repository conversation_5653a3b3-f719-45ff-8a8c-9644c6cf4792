@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600&display=swap');
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

/* Lobe Chat 颜色体系 - 浅色主题 */
:root,
[data-theme="light"] {
  --foreground-rgb: 32, 33, 36;
  --background-start-rgb: 248, 249, 250;
  --background-end-rgb: 255, 255, 255;

  /* 主色调 - Lobe Chat 蓝色系 */
  --primary-color: #1a73e8;
  --primary-hover: #1557b0;
  --secondary-color: #34a853;
  --warning-color: #fbbc04;
  --danger-color: #ea4335;
  --info-color: #1a73e8;

  /* 背景色 */
  --background-color: #f8f9fa;
  --surface-color: #ffffff;
  --container-color: #ffffff;
  --elevated-color: #ffffff;
  --spotlight-color: #ffffff;

  /* 文字颜色 - Google Material Design */
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-tertiary: #80868b;
  --text-quaternary: #9aa0a6;

  /* 边框颜色 */
  --border-color: #dadce0;
  --border-secondary: #e8eaed;

  /* 填充色 */
  --fill-color: #f1f3f4;
  --fill-secondary: #f8f9fa;
  --fill-tertiary: #ffffff;
  --fill-quaternary: #ffffff;

  /* 设计系统 */
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-sm: 6px;
  --border-radius-xs: 4px;

  /* 阴影 - Lobe Chat 风格 */
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-secondary: 0 4px 16px rgba(0, 0, 0, 0.08);
  --shadow-tertiary: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-hover: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);

  /* 动画 */
  --transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  --motion-duration-fast: 0.1s;
  --motion-duration-mid: 0.2s;
  --motion-duration-slow: 0.3s;
  --motion-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --motion-ease-out: cubic-bezier(0.0, 0, 0.2, 1);

  /* 字体大小变量 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 22px;
  --font-size-2xl: 24px;
}

/* Lobe Chat 颜色体系 - 深色主题 */
[data-theme="dark"] {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 20, 20, 20;
  --background-end-rgb: 31, 31, 31;

  /* 背景色 - 深色但不过于暗沉 */
  --background-color: #141414;
  --surface-color: #1f1f1f;
  --container-color: #2d2d2d;
  --elevated-color: #383838;
  --spotlight-color: #2d2d2d;

  /* 文字颜色 - 高对比度但不刺眼 */
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-tertiary: #8c8c8c;
  --text-quaternary: #595959;

  /* 边框颜色 - 适中的对比度 */
  --border-color: #434343;
  --border-secondary: #303030;

  /* 填充色 - 层次分明的深色 */
  --fill-color: #262626;
  --fill-secondary: #1f1f1f;
  --fill-tertiary: #141414;
  --fill-quaternary: #0f0f0f;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: var(--font-size-base);
  height: 100vh;
  margin: 0;
  padding: 0;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  /* 提高合成层级，强制硬件加速，否则会有渲染黑边出现 */
  will-change: opacity;
  transform: translateZ(0);
}

a {
  color: inherit;
  text-decoration: none;
}

/* 自定义滚动条样式 */
* {
  scrollbar-color: var(--border-color) transparent;
  scrollbar-width: thin;
}

::-webkit-scrollbar {
  width: 0.75em;
  height: 0.75em;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
}

:hover::-webkit-scrollbar-thumb {
  border: 3px solid transparent;
  background-color: var(--text-secondary);
  background-clip: content-box;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  body {
    font-size: var(--font-size-sm);
  }

  /* 移动端触摸优化 */
  * {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* 移动端按钮最小触摸区域 */
  .ant-btn {
    min-height: 44px;
    min-width: 44px;
  }

  /* 移动端输入框优化 */
  .ant-input,
  .ant-input-affix-wrapper {
    min-height: 44px;
  }

  /* 移动端卡片间距优化 */
  .ant-card {
    margin: 8px;
  }

  /* 移动端模态框优化 */
  .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  /* 移动端抽屉优化 */
  .ant-drawer-content-wrapper {
    max-width: 85vw;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  body {
    font-size: var(--font-size-xs);
  }

  /* 超小屏幕按钮优化 */
  .ant-btn-sm {
    min-height: 36px;
    min-width: 36px;
    font-size: 12px;
  }

  /* 超小屏幕卡片优化 */
  .ant-card {
    margin: 4px;
  }

  /* 超小屏幕模态框优化 */
  .ant-modal {
    margin: 8px;
    max-width: calc(100vw - 16px);
  }
}

/* 禁用选择文本 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(26, 115, 232, 0.3);
  border-radius: 50%;
  border-top-color: #1a73e8;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Ant Design 样式覆盖 */
.ant-btn {
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.ant-input {
  border-radius: var(--border-radius);
}

.ant-card {
  border-radius: 200px;
  box-shadow: var(--shadow);
}

.ant-modal {
  border-radius: 12px;
}

/* 自定义工具类 */
.glass-effect {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 移动端优化工具类 */
.mobile-safe-area {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

.mobile-touch-target {
  min-height: 44px;
  min-width: 44px;
}

.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.mobile-no-zoom {
  touch-action: manipulation;
}

.mobile-full-height {
  height: 100vh;
  height: 100dvh; /* 动态视口高度 */
}

/* 链接按钮样式修复 */
.ant-btn-link {
  color: var(--primary-color) !important;
}

.ant-btn-link:hover {
  color: var(--primary-hover) !important;
}

.ant-btn-link:focus,
.ant-btn-link:active {
  color: var(--primary-hover) !important;
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}